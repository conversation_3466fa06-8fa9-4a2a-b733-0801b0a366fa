import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#F2F1ED' }}>
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <nav className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-all duration-300" style={{ backgroundColor: '#700014' }}>
                <span className="text-white font-bold text-xl">📖</span>
              </div>
              <div>
                <span className="text-2xl font-bold" style={{ color: '#161616' }}>
                  LoveBook
                </span>
                <p className="text-xs font-medium" style={{ color: '#700014' }}>Создаем истории любви</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/templates" className="font-medium transition-all duration-300 hover:scale-105" style={{ color: '#700014' }}>
                Шаблоны
              </Link>
              <Link href="/examples" className="font-medium transition-all duration-300 hover:scale-105" style={{ color: '#700014' }}>
                Примеры
              </Link>
              <Link href="/pricing" className="font-medium transition-all duration-300 hover:scale-105" style={{ color: '#700014' }}>
                Цены
              </Link>
              <Link
                href="/dashboard"
                className="text-white px-8 py-3 rounded-full font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                style={{ backgroundColor: '#700014' }}
              >
                Создать книгу
              </Link>
            </div>
            <button className="md:hidden p-3 rounded-lg hover:bg-gray-100 transition-all duration-300">
              <span style={{ color: '#700014' }}>☰</span>
            </button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden" style={{ backgroundColor: '#161616' }}>
        {/* Background overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-transparent z-10"></div>

        {/* Background image placeholder - in real implementation, use actual romantic image */}
        <div className="absolute inset-0 bg-gradient-to-br from-amber-900/20 via-rose-900/30 to-orange-900/20"></div>

        {/* Decorative elements */}
        <div className="absolute top-20 right-20 w-32 h-32 rounded-full opacity-10" style={{ backgroundColor: '#D3BF9F' }}></div>
        <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full opacity-10" style={{ backgroundColor: '#700014' }}></div>

        <div className="relative z-20 container mx-auto px-6 text-center text-white">
          <div className="max-w-5xl mx-auto">
            {/* Elegant subtitle */}
            <p className="text-lg md:text-xl font-light mb-6 tracking-wider" style={{ color: '#D3BF9F' }}>
              ✦ Создаем персональные истории любви ✦
            </p>

            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="block mb-4">Превратите</span>
              <span className="block" style={{ color: '#D3BF9F' }}>воспоминания</span>
              <span className="block">в книгу</span>
            </h1>

            <p className="text-xl md:text-2xl mb-12 leading-relaxed max-w-3xl mx-auto font-light" style={{ color: '#F2F1ED' }}>
              Создайте уникальную персональную книгу, которая сохранит ваши самые дорогие моменты
              и станет особенным подарком на всю жизнь
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <Link
                href="/dashboard"
                className="text-white px-10 py-4 rounded-full font-semibold text-lg hover:opacity-90 transition-all duration-500 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105"
                style={{ backgroundColor: '#700014' }}
              >
                Создать мою книгу
              </Link>
              <button className="px-10 py-4 rounded-full font-medium border-2 hover:bg-white/10 transition-all duration-500 backdrop-blur-sm" style={{ color: '#D3BF9F', borderColor: '#D3BF9F' }}>
                Посмотреть примеры
              </button>
            </div>

            {/* Scroll indicator */}
            <div className="animate-bounce">
              <div className="w-6 h-10 border-2 rounded-full flex justify-center" style={{ borderColor: '#D3BF9F' }}>
                <div className="w-1 h-3 rounded-full mt-2 animate-pulse" style={{ backgroundColor: '#D3BF9F' }}></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-3 transition-all duration-500 group-hover:scale-110" style={{ color: '#700014' }}>
                15,000+
              </div>
              <div className="text-lg font-medium" style={{ color: '#161616' }}>Созданных книг</div>
              <div className="text-sm mt-1" style={{ color: '#700014' }}>Счастливых историй</div>
            </div>
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-3 transition-all duration-500 group-hover:scale-110" style={{ color: '#D3BF9F' }}>
                98%
              </div>
              <div className="text-lg font-medium" style={{ color: '#161616' }}>Довольных клиентов</div>
              <div className="text-sm mt-1" style={{ color: '#700014' }}>Положительных отзывов</div>
            </div>
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-3 transition-all duration-500 group-hover:scale-110" style={{ color: '#700014' }}>
                50+
              </div>
              <div className="text-lg font-medium" style={{ color: '#161616' }}>Шаблонов</div>
              <div className="text-sm mt-1" style={{ color: '#700014' }}>Уникальных дизайнов</div>
            </div>
            <div className="group">
              <div className="text-5xl md:text-6xl font-bold mb-3 transition-all duration-500 group-hover:scale-110" style={{ color: '#D3BF9F' }}>
                24/7
              </div>
              <div className="text-lg font-medium" style={{ color: '#161616' }}>Поддержка</div>
              <div className="text-sm mt-1" style={{ color: '#700014' }}>Всегда рядом</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <p className="text-lg font-medium mb-4 tracking-wider" style={{ color: '#D3BF9F' }}>
            ✦ ЭКСКЛЮЗИВНЫЕ УСЛУГИ ДЛЯ ВАС ✦
          </p>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight" style={{ color: '#161616' }}>
            Два способа создать
            <span style={{ color: '#700014' }}> вашу историю</span>
          </h2>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: '#700014' }}>
            Выберите подходящий формат для создания персональной книги, которая станет особенным подарком
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Quiz Book */}
          <div className="group bg-white rounded-3xl p-10 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-4 border-2 relative overflow-hidden" style={{ borderColor: '#D3BF9F' }}>
            {/* Decorative background element */}
            <div className="absolute top-0 right-0 w-32 h-32 rounded-full opacity-5 transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-all duration-500" style={{ backgroundColor: '#700014' }}></div>

            <div className="relative z-10">
              <div className="w-20 h-20 rounded-3xl flex items-center justify-center mb-8 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#700014' }}>
                <span className="text-white text-3xl">📝</span>
              </div>

              <h3 className="text-3xl font-bold mb-6 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
                Книга-опросник
              </h3>

              <p className="mb-8 leading-relaxed text-lg" style={{ color: '#700014' }}>
                Выберите готовый шаблон для мамы, папы, друга или любимого человека.
                Ответьте на трогательные вопросы о ваших отношениях и воспоминаниях.
              </p>

              <ul className="space-y-4 mb-10">
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-3 h-3 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}></span>
                  Готовые шаблоны для разных отношений
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-3 h-3 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}></span>
                  Персонализация вопросов
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-3 h-3 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}></span>
                  Настройка стиля и обложки
                </li>
              </ul>

              <Link
                href="/dashboard?type=quiz"
                className="inline-flex items-center font-bold text-lg hover:opacity-80 transition-all duration-300 group-hover:translate-x-2"
                style={{ color: '#700014' }}
              >
                Создать книгу-опросник
                <span className="ml-3 text-xl">→</span>
              </Link>
            </div>
          </div>

          {/* Story Book */}
          <div className="group bg-white rounded-3xl p-10 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-4 border-2 relative overflow-hidden" style={{ borderColor: '#D3BF9F' }}>
            {/* Decorative background element */}
            <div className="absolute top-0 right-0 w-32 h-32 rounded-full opacity-5 transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}></div>

            <div className="relative z-10">
              <div className="w-20 h-20 rounded-3xl flex items-center justify-center mb-8 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}>
                <span className="text-white text-3xl">📖</span>
              </div>

              <h3 className="text-3xl font-bold mb-6 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
                Книга-история
              </h3>

              <p className="mb-8 leading-relaxed text-lg" style={{ color: '#700014' }}>
                Создайте уникальную историю с нуля. Добавляйте текст, изображения
                и даже аудиозаписи, которые автоматически превратятся в текст.
              </p>

              <ul className="space-y-4 mb-10">
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-3 h-3 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}></span>
                  Свободное творчество
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-3 h-3 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}></span>
                  Добавление изображений
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-3 h-3 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}></span>
                  Транскрипция аудио в текст
                </li>
              </ul>

              <Link
                href="/dashboard?type=story"
                className="inline-flex items-center font-bold text-lg hover:opacity-80 transition-all duration-300 group-hover:translate-x-2"
                style={{ color: '#700014' }}
              >
                Создать книгу-историю
                <span className="ml-3 text-xl">→</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-24 relative overflow-hidden" style={{ backgroundColor: '#161616' }}>
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-96 h-96 rounded-full opacity-5 transform -translate-x-48 -translate-y-48" style={{ backgroundColor: '#D3BF9F' }}></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full opacity-5 transform translate-x-48 translate-y-48" style={{ backgroundColor: '#700014' }}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <p className="text-lg font-medium mb-4 tracking-wider" style={{ color: '#D3BF9F' }}>
              ✦ МЫ МОЖЕМ СОЗДАТЬ ПРЕКРАСНЫЕ ВЕЩИ ВМЕСТЕ ✦
            </p>
            <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight text-white">
              Как это
              <span style={{ color: '#D3BF9F' }}> работает</span>
            </h2>
            <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: '#F2F1ED' }}>
              Простой и элегантный процесс создания вашей персональной книги за три шага
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 max-w-6xl mx-auto">
            <div className="group text-center">
              <div className="relative mb-8">
                <div className="w-28 h-28 rounded-full flex items-center justify-center mx-auto shadow-2xl group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#700014' }}>
                  <span className="text-white text-4xl font-bold">1</span>
                </div>
                {/* Connecting line */}
                <div className="hidden md:block absolute top-14 left-full w-full h-0.5 opacity-30" style={{ backgroundColor: '#D3BF9F' }}></div>
              </div>
              <h3 className="text-2xl font-bold mb-6 text-white group-hover:text-opacity-90 transition-all duration-300">
                Выберите тип книги
              </h3>
              <p className="text-lg leading-relaxed" style={{ color: '#F2F1ED' }}>
                Определитесь с форматом: книга-опросник с готовыми вопросами или свободная книга-история для полного творчества
              </p>
            </div>

            <div className="group text-center">
              <div className="relative mb-8">
                <div className="w-28 h-28 rounded-full flex items-center justify-center mx-auto shadow-2xl group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}>
                  <span className="text-white text-4xl font-bold">2</span>
                </div>
                {/* Connecting line */}
                <div className="hidden md:block absolute top-14 left-full w-full h-0.5 opacity-30" style={{ backgroundColor: '#D3BF9F' }}></div>
              </div>
              <h3 className="text-2xl font-bold mb-6 text-white group-hover:text-opacity-90 transition-all duration-300">
                Создайте содержание
              </h3>
              <p className="text-lg leading-relaxed" style={{ color: '#F2F1ED' }}>
                Ответьте на трогательные вопросы или напишите свою историю, добавьте любимые фотографии и аудиозаписи
              </p>
            </div>

            <div className="group text-center">
              <div className="relative mb-8">
                <div className="w-28 h-28 rounded-full flex items-center justify-center mx-auto shadow-2xl group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#700014' }}>
                  <span className="text-white text-4xl font-bold">3</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-6 text-white group-hover:text-opacity-90 transition-all duration-300">
                Получите книгу
              </h3>
              <p className="text-lg leading-relaxed" style={{ color: '#F2F1ED' }}>
                Настройте элегантный дизайн, выберите премиум обложку и получите готовую печатную книгу высочайшего качества
              </p>
            </div>
          </div>

          {/* Call to action */}
          <div className="text-center mt-16">
            <Link
              href="/dashboard"
              className="inline-flex items-center text-white px-10 py-4 rounded-full font-semibold text-lg hover:opacity-90 transition-all duration-500 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105"
              style={{ backgroundColor: '#700014' }}
            >
              Начать создание
              <span className="ml-3 text-xl">→</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Showcase */}
      <section className="container mx-auto px-6 py-24">
        <div className="text-center mb-20">
          <p className="text-lg font-medium mb-4 tracking-wider" style={{ color: '#D3BF9F' }}>
            ✦ НАШИ ПОСЛЕДНИЕ ПРОЕКТЫ ✦
          </p>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight" style={{ color: '#161616' }}>
            Почему выбирают
            <span style={{ color: '#700014' }}> LoveBook</span>
          </h2>
          <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: '#700014' }}>
            Мы предлагаем уникальные возможности и профессиональный подход для создания персональных книг высочайшего качества
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-10 max-w-7xl mx-auto">
          <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
            <div className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#700014' }}>
              <span className="text-white text-3xl">🎨</span>
            </div>
            <h3 className="text-2xl font-bold mb-4 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
              Профессиональный дизайн
            </h3>
            <p className="text-lg leading-relaxed" style={{ color: '#700014' }}>
              Более 20 элегантных стилей оформления и сотни премиум шрифтов для создания уникального дизайна
            </p>
          </div>

          <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
            <div className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}>
              <span className="text-white text-3xl">🎤</span>
            </div>
            <h3 className="text-2xl font-bold mb-4 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
              Аудио в текст
            </h3>
            <p className="text-lg leading-relaxed" style={{ color: '#700014' }}>
              Записывайте истории голосом - наша AI технология автоматически превратит их в красивый текст
            </p>
          </div>

          <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
            <div className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#700014' }}>
              <span className="text-white text-3xl">📱</span>
            </div>
            <h3 className="text-2xl font-bold mb-4 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
              Мобильное приложение
            </h3>
            <p className="text-lg leading-relaxed" style={{ color: '#700014' }}>
              Создавайте книги где угодно с нашим интуитивным мобильным приложением
            </p>
          </div>

          <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
            <div className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}>
              <span className="text-white text-3xl">🚚</span>
            </div>
            <h3 className="text-2xl font-bold mb-4 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
              Экспресс доставка
            </h3>
            <p className="text-lg leading-relaxed" style={{ color: '#700014' }}>
              Профессиональная печать и доставка готовой книги в течение 3-5 рабочих дней
            </p>
          </div>

          <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
            <div className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#700014' }}>
              <span className="text-white text-3xl">💎</span>
            </div>
            <h3 className="text-2xl font-bold mb-4 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
              Премиум качество
            </h3>
            <p className="text-lg leading-relaxed" style={{ color: '#700014' }}>
              Плотная бумага премиум класса, твердая обложка и профессиональная печать высочайшего качества
            </p>
          </div>

          <div className="group text-center p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
            <div className="w-20 h-20 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}>
              <span className="text-white text-3xl">🔒</span>
            </div>
            <h3 className="text-2xl font-bold mb-4 group-hover:text-opacity-90 transition-all duration-300" style={{ color: '#161616' }}>
              Безопасность данных
            </h3>
            <p className="text-lg leading-relaxed" style={{ color: '#700014' }}>
              Ваши личные истории и фотографии защищены банковским уровнем шифрования
            </p>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 relative overflow-hidden" style={{ backgroundColor: '#F2F1ED' }}>
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 rounded-full opacity-10 transform translate-x-32 -translate-y-32" style={{ backgroundColor: '#700014' }}></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 rounded-full opacity-10 transform -translate-x-32 translate-y-32" style={{ backgroundColor: '#D3BF9F' }}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <p className="text-lg font-medium mb-4 tracking-wider" style={{ color: '#D3BF9F' }}>
              ✦ ОТЗЫВЫ ✦
            </p>
            <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight" style={{ color: '#161616' }}>
              Что говорят наши
              <span style={{ color: '#700014' }}> клиенты</span>
            </h2>
            <div className="flex justify-center items-center space-x-2 mb-6">
              {[...Array(5)].map((_, i) => (
                <span key={i} className="text-yellow-400 text-3xl">⭐</span>
              ))}
              <span className="ml-4 text-xl font-semibold" style={{ color: '#700014' }}>4.9/5 на основе 2,847 отзывов</span>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-10 max-w-7xl mx-auto mb-16">
            <div className="group bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
              {/* Quote decoration */}
              <div className="absolute top-6 right-6 text-6xl opacity-10" style={{ color: '#700014' }}>&ldquo;</div>

              <div className="flex items-center mb-6">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mr-4 shadow-lg" style={{ backgroundColor: '#700014' }}>
                  <span className="text-white font-bold text-xl">А</span>
                </div>
                <div>
                  <h4 className="font-bold text-xl" style={{ color: '#161616' }}>Анна К.</h4>
                  <p className="font-medium" style={{ color: '#D3BF9F' }}>Подарок маме</p>
                  <div className="flex space-x-1 mt-2">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-lg">⭐</span>
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-lg leading-relaxed italic" style={{ color: '#700014' }}>
                "                &ldquo;Создала книгу-опросник для мамы на день рождения. Она плакала от счастья!
                Такой личный и трогательный подарок. Качество печати превзошло все ожидания.&rdquo;"
              </p>
            </div>

            <div className="group bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
              {/* Quote decoration */}
              <div className="absolute top-6 right-6 text-6xl opacity-10" style={{ color: '#D3BF9F' }}>&ldquo;</div>

              <div className="flex items-center mb-6">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mr-4 shadow-lg" style={{ backgroundColor: '#D3BF9F' }}>
                  <span className="text-white font-bold text-xl">М</span>
                </div>
                <div>
                  <h4 className="font-bold text-xl" style={{ color: '#161616' }}>Михаил С.</h4>
                  <p className="font-medium" style={{ color: '#D3BF9F' }}>История семьи</p>
                  <div className="flex space-x-1 mt-2">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-lg">⭐</span>
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-lg leading-relaxed italic" style={{ color: '#700014' }}>
                "                &ldquo;Записал истории дедушки и превратил их в красивую книгу.
                Теперь эти воспоминания сохранятся для будущих поколений. Процесс был невероятно простым!&rdquo;"
              </p>
            </div>

            <div className="group bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
              {/* Quote decoration */}
              <div className="absolute top-6 right-6 text-6xl opacity-10" style={{ color: '#700014' }}>&ldquo;</div>

              <div className="flex items-center mb-6">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mr-4 shadow-lg" style={{ backgroundColor: '#700014' }}>
                  <span className="text-white font-bold text-xl">Е</span>
                </div>
                <div>
                  <h4 className="font-bold text-xl" style={{ color: '#161616' }}>Елена Д.</h4>
                  <p className="font-medium" style={{ color: '#D3BF9F' }}>Подарок подруге</p>
                  <div className="flex space-x-1 mt-2">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className="text-yellow-400 text-lg">⭐</span>
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-lg leading-relaxed italic" style={{ color: '#700014' }}>
                "                &ldquo;Сделала книгу о нашей дружбе для лучшей подруги.
                Процесс создания был таким же приятным, как и результат! Рекомендую всем.&rdquo;"
              </p>
            </div>
          </div>

          {/* Featured testimonial */}
          <div className="max-w-5xl mx-auto text-center">
            <div className="relative rounded-3xl p-12 shadow-2xl overflow-hidden" style={{ backgroundColor: '#161616' }}>
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-32 h-32 rounded-full opacity-10 transform -translate-x-16 -translate-y-16" style={{ backgroundColor: '#D3BF9F' }}></div>
              <div className="absolute bottom-0 right-0 w-32 h-32 rounded-full opacity-10 transform translate-x-16 translate-y-16" style={{ backgroundColor: '#700014' }}></div>

              <div className="relative z-10">
                <div className="w-28 h-28 rounded-full flex items-center justify-center mx-auto mb-8 shadow-2xl" style={{ backgroundColor: '#700014' }}>
                  <span className="text-white text-5xl">▶</span>
                </div>
                <h3 className="text-3xl md:text-4xl font-bold mb-6 text-white">
                  Посмотрите, как наши клиенты создают свои книги
                </h3>
                <p className="text-xl mb-8 leading-relaxed" style={{ color: '#F2F1ED' }}>
                  Реальные истории людей, которые превратили свои воспоминания в прекрасные книги
                </p>
                <button className="text-white px-10 py-4 rounded-full font-semibold text-lg hover:opacity-90 transition-all duration-500 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105" style={{ backgroundColor: '#700014' }}>
                  Смотреть видео-истории
                </button>
              </div>
            </div>
          </div>

          {/* Call to action */}
          <div className="text-center mt-16">
            <p className="text-xl mb-8" style={{ color: '#700014' }}>
              Готовы создать свою уникальную книгу?
            </p>
            <Link
              href="/dashboard"
              className="inline-flex items-center text-white px-12 py-5 rounded-full font-bold text-xl hover:opacity-90 transition-all duration-500 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 hover:scale-105"
              style={{ backgroundColor: '#700014' }}
            >
              Начать создание книги
              <span className="ml-4 text-2xl">→</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-20">
            <p className="text-lg font-medium mb-4 tracking-wider" style={{ color: '#D3BF9F' }}>
              ✦ ДАВАЙТЕ СПЛАНИРУЕМ ВАШЕ СЛЕДУЮЩЕЕ СОБЫТИЕ ВМЕСТЕ ✦
            </p>
            <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight" style={{ color: '#161616' }}>
              Простые и честные
              <span style={{ color: '#700014' }}> цены</span>
            </h2>
            <p className="text-xl max-w-4xl mx-auto leading-relaxed" style={{ color: '#700014' }}>
              Выберите подходящий план для создания вашей персональной книги высочайшего качества
            </p>
          </div>
        

        <div className="grid md:grid-cols-3 gap-10 max-w-6xl mx-auto">
          {/* Basic Plan */}
          <div className="group bg-white rounded-3xl p-10 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-2 relative overflow-hidden" style={{ borderColor: '#D3BF9F' }}>
            {/* Decorative background element */}
            <div className="absolute top-0 right-0 w-32 h-32 rounded-full opacity-5 transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}></div>

            <div className="relative z-10">
              <div className="text-center mb-10">
                <h3 className="text-3xl font-bold mb-6" style={{ color: '#161616' }}>Базовый</h3>
                <div className="text-5xl font-bold mb-3" style={{ color: '#700014' }}>
                  1,990₽
                </div>
                <p className="text-lg font-medium" style={{ color: '#D3BF9F' }}>За одну книгу</p>
              </div>
              <ul className="space-y-5 mb-10">
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  До 50 страниц
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  5 готовых шаблонов
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Мягкая обложка
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#D3BF9F' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Бесплатная доставка
                </li>
              </ul>
              <Link
                href="/dashboard?plan=basic"
                className="w-full py-4 rounded-full font-semibold text-lg text-center block hover:opacity-90 transition-all duration-500 shadow-lg hover:shadow-xl transform hover:scale-105 border-2"
                style={{ color: '#700014', borderColor: '#700014' }}
              >
                Выбрать план
              </Link>
            </div>
          </div>

          {/* Premium Plan - Most Popular */}
          <div className="group rounded-3xl p-10 shadow-2xl transform scale-105 relative overflow-hidden" style={{ backgroundColor: '#700014' }}>
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
              <span className="text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg" style={{ backgroundColor: '#D3BF9F' }}>
                ✦ ПОПУЛЯРНЫЙ ✦
              </span>
            </div>

            {/* Decorative background elements */}
            <div className="absolute top-0 right-0 w-40 h-40 rounded-full opacity-10 transform translate-x-20 -translate-y-20 group-hover:scale-150 transition-all duration-500" style={{ backgroundColor: '#D3BF9F' }}></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full opacity-10 transform -translate-x-16 translate-y-16 group-hover:scale-150 transition-all duration-500" style={{ backgroundColor: '#F2F1ED' }}></div>

            <div className="relative z-10">
              <div className="text-center mb-10">
                <h3 className="text-3xl font-bold mb-6 text-white">Премиум</h3>
                <div className="text-5xl font-bold mb-3 text-white">
                  2,990₽
                </div>
                <p className="text-lg font-medium" style={{ color: '#F2F1ED' }}>За одну книгу</p>
              </div>
              <ul className="space-y-5 mb-10">
                <li className="flex items-center text-lg text-white">
                  <span className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-sm font-bold" style={{ color: '#700014' }}>✓</span>
                  </span>
                  До 100 страниц
                </li>
                <li className="flex items-center text-lg text-white">
                  <span className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-sm font-bold" style={{ color: '#700014' }}>✓</span>
                  </span>
                  Все шаблоны (50+)
                </li>
                <li className="flex items-center text-lg text-white">
                  <span className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-sm font-bold" style={{ color: '#700014' }}>✓</span>
                  </span>
                  Твердая обложка премиум
                </li>
                <li className="flex items-center text-lg text-white">
                  <span className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-sm font-bold" style={{ color: '#700014' }}>✓</span>
                  </span>
                  Аудио транскрипция
                </li>
                <li className="flex items-center text-lg text-white">
                  <span className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-sm font-bold" style={{ color: '#700014' }}>✓</span>
                  </span>
                  Приоритетная поддержка
                </li>
              </ul>
              <Link
                href="/dashboard?plan=premium"
                className="w-full bg-white py-4 rounded-full font-bold text-lg text-center block hover:bg-gray-50 transition-all duration-500 shadow-2xl hover:shadow-3xl transform hover:scale-105"
                style={{ color: '#700014' }}
              >
                Выбрать план
              </Link>
            </div>
          </div>

          {/* Deluxe Plan */}
          <div className="group bg-white rounded-3xl p-10 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-2 relative overflow-hidden" style={{ borderColor: '#D3BF9F' }}>
            {/* Decorative background element */}
            <div className="absolute top-0 right-0 w-32 h-32 rounded-full opacity-5 transform translate-x-16 -translate-y-16 group-hover:scale-150 transition-all duration-500" style={{ backgroundColor: '#700014' }}></div>

            <div className="relative z-10">
              <div className="text-center mb-10">
                <h3 className="text-3xl font-bold mb-6" style={{ color: '#161616' }}>Делюкс</h3>
                <div className="text-5xl font-bold mb-3" style={{ color: '#700014' }}>
                  4,990₽
                </div>
                <p className="text-lg font-medium" style={{ color: '#D3BF9F' }}>За одну книгу</p>
              </div>
              <ul className="space-y-5 mb-10">
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Неограниченно страниц
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Персональный дизайн
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Премиум материалы
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Подарочная упаковка
                </li>
                <li className="flex items-center text-lg" style={{ color: '#700014' }}>
                  <span className="w-6 h-6 rounded-full flex items-center justify-center mr-4 flex-shrink-0" style={{ backgroundColor: '#700014' }}>
                    <span className="text-white text-sm font-bold">✓</span>
                  </span>
                  Персональный менеджер
                </li>
              </ul>
              <Link
                href="/dashboard?plan=deluxe"
                className="w-full text-white py-4 rounded-full font-bold text-lg text-center block hover:opacity-90 transition-all duration-500 shadow-lg hover:shadow-xl transform hover:scale-105"
                style={{ backgroundColor: '#700014' }}
              >
                Выбрать план
              </Link>
            </div>
          </div>
        </div>

        <div className="text-center mt-16">
          <p className="text-xl mb-6 font-medium" style={{ color: '#700014' }}>
            💳 Принимаем все виды оплаты • 🔒 Безопасные платежи • 📞 Поддержка 24/7
          </p>
          <p className="text-lg" style={{ color: '#D3BF9F' }}>
            * Цены указаны за одну книгу. Скидки при заказе нескольких экземпляров.
          </p>
        </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-white dark:bg-gray-900 py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-800 dark:text-gray-200">
              Часто задаваемые
              <span className="bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent"> вопросы</span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Ответы на самые популярные вопросы о создании персональных книг
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">
                Сколько времени занимает создание книги?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Создание контента зависит от вас - от 30 минут до нескольких дней.
                После завершения работы над книгой, печать и доставка занимают 3-5 рабочих дней.
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">
                Можно ли редактировать книгу после создания?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Да, вы можете редактировать содержание, дизайн и макет до момента отправки в печать.
                После печати изменения невозможны, но вы можете создать новую версию.
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">
                Какое качество печати и материалов?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Мы используем плотную бумагу 170г/м², профессиональную цифровую печать высокого разрешения.
                Твердая обложка изготавливается из картона с ламинацией для долговечности.
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">
                Можно ли заказать несколько экземпляров?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Конечно! При заказе от 3 экземпляров действует скидка 15%, от 5 экземпляров - 25%.
                Это отличный вариант для подарков всей семье.
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">
                Безопасны ли мои личные данные и фотографии?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Абсолютно. Все данные шифруются и хранятся на защищенных серверах.
                Мы не передаем информацию третьим лицам и удаляем файлы после печати по вашему запросу.
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6">
              <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-gray-200">
                Что если я не доволен результатом?
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Мы гарантируем качество! Если книга не соответствует ожиданиям по нашей вине,
                мы бесплатно перепечатаем её или вернем деньги в течение 30 дней.
              </p>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Не нашли ответ на свой вопрос?
            </p>
            <Link
              href="/support"
              className="inline-flex items-center text-rose-600 font-semibold hover:text-rose-700 transition-colors"
            >
              Связаться с поддержкой
              <span className="ml-2">→</span>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-rose-500 via-pink-500 to-orange-500 py-20">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Начните создавать вашу книгу уже сегодня
          </h2>
          <p className="text-xl text-rose-100 mb-8 max-w-3xl mx-auto">
            Превратите ваши воспоминания в настоящую книгу, которая будет радовать долгие годы
          </p>
          <Link
            href="/dashboard"
            className="bg-white text-rose-600 px-8 py-4 rounded-full font-semibold text-lg hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block"
          >
            Создать мою книгу
          </Link>
        </div>
      </section>

      {/* Trust Signals */}
      <section className="bg-gray-50 dark:bg-gray-800 py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800 dark:text-gray-200">
              Нам доверяют тысячи семей
            </h2>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-items-center opacity-60">
            {/* Trust badges - placeholder for actual logos */}
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
              <div className="text-center">
                <div className="text-2xl mb-2">🔒</div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-300">SSL Защита</div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
              <div className="text-center">
                <div className="text-2xl mb-2">💳</div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-300">Безопасная оплата</div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
              <div className="text-center">
                <div className="text-2xl mb-2">🏆</div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-300">Премия качества</div>
              </div>
            </div>
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
              <div className="text-center">
                <div className="text-2xl mb-2">📞</div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-300">Поддержка 24/7</div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <div className="inline-flex items-center space-x-6 text-gray-600 dark:text-gray-300">
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                <span>Гарантия качества</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                <span>Возврат средств</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                <span>Быстрая доставка</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="text-white py-16" style={{ backgroundColor: '#161616' }}>
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{ backgroundColor: '#700014' }}>
                  <span className="text-white font-bold text-xl">❤</span>
                </div>
                <span className="text-3xl font-bold">LoveBook</span>
              </div>
              <p className="mb-6 max-w-md" style={{ color: '#F2F1ED' }}>
                Превращаем ваши самые дорогие воспоминания в красивые персональные книги.
                Создано с любовью для сохранения особенных моментов жизни.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 rounded-full flex items-center justify-center transition-colors" style={{ backgroundColor: '#D3BF9F' }}>
                  <span className="text-lg">📘</span>
                </a>
                <a href="#" className="w-10 h-10 rounded-full flex items-center justify-center transition-colors" style={{ backgroundColor: '#D3BF9F' }}>
                  <span className="text-lg">📷</span>
                </a>
                <a href="#" className="w-10 h-10 rounded-full flex items-center justify-center transition-colors" style={{ backgroundColor: '#D3BF9F' }}>
                  <span className="text-lg">💬</span>
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Быстрые ссылки</h3>
              <ul className="space-y-2">
                <li><Link href="/how-it-works" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Как это работает</Link></li>
                <li><Link href="/templates" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Шаблоны</Link></li>
                <li><Link href="/examples" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Примеры книг</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Цены</Link></li>
                <li><Link href="/blog" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Блог</Link></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Поддержка</h3>
              <ul className="space-y-2">
                <li><Link href="/support" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Центр помощи</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Связаться с нами</Link></li>
                <li><Link href="/shipping" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Доставка</Link></li>
                <li><Link href="/returns" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Возврат</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Конфиденциальность</Link></li>
              </ul>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t pt-8" style={{ borderColor: '#D3BF9F' }}>
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-center md:text-left mb-4 md:mb-0" style={{ color: '#F2F1ED' }}>
                <p>&copy; 2024 LoveBook. Все права защищены. Создано с любовью для сохранения воспоминаний.</p>
              </div>
              <div className="flex space-x-6 text-sm">
                <Link href="/terms" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Условия использования</Link>
                <Link href="/privacy" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Политика конфиденциальности</Link>
                <Link href="/cookies" className="hover:text-white transition-colors" style={{ color: '#F2F1ED' }}>Cookies</Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
